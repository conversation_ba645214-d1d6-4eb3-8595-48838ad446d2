import { Compo<PERSON>, <PERSON><PERSON>ni<PERSON>, <PERSON><PERSON><PERSON>roy, ChangeDetectionStrategy, ChangeDetectorRef, HostListener, inject } from '@angular/core';
import { Subject, fromEvent } from 'rxjs';
import { debounceTime, takeUntil } from 'rxjs/operators';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { TopNavbarComponent } from '../top-navbar/top-navbar.component';
import { SidebarComponent } from '../sidebar/sidebar.component';
import { FormsModule } from '@angular/forms';
import { TransactionService } from '../services/transaction.service';
import { BudgetDataService } from '../core/budget/budget-data.service';
import { NotificationService } from '../notification-system/notification.service';
import { AuthService } from '../core/auth/auth.service';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { AddTicketModalComponent } from '../add-ticket-modal/add-ticket-modal.component';
import { BudgetSelectionModalComponent } from '../budget-selection-modal/budget-selection-modal.component';

@Component({
  selector: 'app-shopper-receet',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    TopNavbarComponent,
    SidebarComponent,
    FormsModule,
    AddTicketModalComponent,
    BudgetSelectionModalComponent
  ],
  templateUrl: './shopper-receet.component.html',
  styleUrls: ['./shopper-receet.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ShopperReceetComponent implements OnInit, OnDestroy {
  // Inject AuthService
  private authService = inject(AuthService);
  // Original transaction data that will be preserved
  private originalTransactions = [
    { ticketNumber: 1, productName: 'Hat', productId: '#20462', date: '13/05/2022', amount: '16 TND', paymentMode: 'Cash', status: 'Loyal', productImage: 'https://placehold.co/24x24', rating: 4.5, brandName: 'Carrefour', brandLogo: 'assets/images/brands/carrefour.png' },
    { ticketNumber: 2, productName: 'Laptop', productId: '#18933', date: '22/05/2022', amount: '2230 TND', paymentMode: 'Cash', status: 'New', productImage: 'https://placehold.co/24x24', rating: 3.8, brandName: 'Zen', brandLogo: 'assets/images/brands/zen.png' },
    {
      ticketNumber: 3,
      productName: 'Laptop, Mouse & Headset',
      productId: '#45169',
      date: '15/06/2022',
      amount: '2509.85 TND',
      paymentMode: 'Cash on Delivery',
      status: 'In progress',
      productImage: 'https://placehold.co/24x24',
      rating: 4.2,
      brandName: 'Monoprix',
      brandLogo: 'assets/images/brands/monoprix.png',
      products: [
        { name: 'Laptop', price: '2230 TND' },
        { name: 'Mouse', price: '49.95 TND' },
        { name: 'Headset', price: '229.90 TND' }
      ]
    },
    { ticketNumber: 4, productName: 'Bag', productId: '#34304', date: '06/09/2022', amount: '999.95 TND', paymentMode: 'Transfer Bank', status: 'In progress', productImage: 'https://placehold.co/24x24', rating: 3.5, brandName: 'Aziza', brandLogo: 'assets/images/brands/aziza.png' },
    { ticketNumber: 5, productName: 'Headset', productId: '#17188', date: '25/09/2022', amount: '229.95 TND', paymentMode: 'Cash on Delivery', status: 'New', productImage: 'https://placehold.co/24x24', rating: 4.7, brandName: 'Carrefour', brandLogo: 'assets/images/brands/carrefour.png' },
    {
      ticketNumber: 6,
      productName: 'Mouse & Keyboard',
      productId: '#73003',
      date: '04/10/2022',
      amount: '6987.95 TND',
      paymentMode: 'Transfer Bank',
      status: 'New',
      productImage: 'https://placehold.co/24x24',
      rating: 4.0,
      brandName: 'Zen',
      brandLogo: 'assets/images/brands/zen.png',
      products: [
        { name: 'Mouse', price: '39.95 TND' },
        { name: 'Keyboard', price: '6948 TND' }
      ]
    },
    { ticketNumber: 7, productName: 'Clock', productId: '#58825', date: '17/10/2022', amount: '174.95 TND', paymentMode: 'Transfer Bank', status: 'New', productImage: 'https://placehold.co/24x24', rating: 3.9, brandName: 'Monoprix', brandLogo: 'assets/images/brands/monoprix.png' },
    { ticketNumber: 8, productName: 'T-shirt', productId: '#44122', date: '24/10/2022', amount: '249.95 TND', paymentMode: 'Cash on Delivery', status: 'New', productImage: 'https://placehold.co/24x24', rating: 4.3, brandName: 'Aziza', brandLogo: 'assets/images/brands/aziza.png' },
    {
      ticketNumber: 9,
      productName: 'Monitor & Webcam',
      productId: '#89094',
      date: '01/11/2022',
      amount: '1148.95 TND',
      paymentMode: 'Transfer Bank',
      status: 'New',
      productImage: 'https://placehold.co/24x24',
      rating: 4.8,
      brandName: 'Carrefour',
      brandLogo: 'assets/images/brands/carrefour.png',
      products: [
        { name: 'Monitor', price: '999.95 TND' },
        { name: 'Webcam', price: '149 TND' }
      ]
    },
    { ticketNumber: 10, productName: 'Keyboard', productId: '#85252', date: '22/11/2022', amount: '6948 TND', paymentMode: 'Transfer Bank', status: 'In progress', productImage: 'https://placehold.co/24x24', rating: 4.1, brandName: 'Zen', brandLogo: 'assets/images/brands/zen.png' },
    { ticketNumber: 11, productName: 'Gaming Chair', productId: '#92145', date: '03/12/2022', amount: '450 TND', paymentMode: 'Transfer Bank', status: 'New', productImage: 'https://placehold.co/24x24', rating: 4.6, brandName: 'Monoprix', brandLogo: 'assets/images/brands/monoprix.png' },
    {
      ticketNumber: 12,
      productName: 'Smartwatch & Earphones',
      productId: '#34567',
      date: '15/12/2022',
      amount: '378 TND',
      paymentMode: 'Cash',
      status: 'Loyal',
      productImage: 'https://placehold.co/24x24',
      rating: 3.7,
      brandName: 'Aziza',
      brandLogo: 'assets/images/brands/aziza.png',
      products: [
        { name: 'Smartwatch', price: '299 TND' },
        { name: 'Earphones', price: '79 TND' }
      ]
    },
    { ticketNumber: 13, productName: 'Bluetooth Speaker', productId: '#78901', date: '28/12/2022', amount: '129 TND', paymentMode: 'Cash on Delivery', status: 'In progress', productImage: 'https://placehold.co/24x24', rating: 4.4, brandName: 'Carrefour', brandLogo: 'assets/images/brands/carrefour.png' },
    { ticketNumber: 14, productName: 'Coffee Maker', productId: '#23456', date: '05/01/2023', amount: '199 TND', paymentMode: 'Transfer Bank', status: 'New', productImage: 'https://placehold.co/24x24', rating: 3.9, brandName: 'Zen', brandLogo: 'assets/images/brands/zen.png' },
    {
      ticketNumber: 15,
      productName: 'Printer & Desk Lamp',
      productId: '#67890',
      date: '18/01/2023',
      amount: '408 TND',
      paymentMode: 'Cash',
      status: 'Loyal',
      productImage: 'https://placehold.co/24x24',
      rating: 4.2,
      brandName: 'Monoprix',
      brandLogo: 'assets/images/brands/monoprix.png',
      products: [
        { name: 'Printer', price: '349 TND' },
        { name: 'Desk Lamp', price: '59 TND' }
      ]
    },
    { ticketNumber: 16, productName: 'Air Purifier', productId: '#12345', date: '02/02/2023', amount: '279 TND', paymentMode: 'Transfer Bank', status: 'New', productImage: 'https://placehold.co/24x24', rating: 4.5, brandName: 'Aziza', brandLogo: 'assets/images/brands/aziza.png' },
    { ticketNumber: 17, productName: 'Desk Lamp', productId: '#56789', date: '15/02/2023', amount: '59 TND', paymentMode: 'Cash on Delivery', status: 'In progress', productImage: 'https://placehold.co/24x24', rating: 3.8, brandName: 'Carrefour', brandLogo: 'assets/images/brands/carrefour.png' },
    {
      ticketNumber: 18,
      productName: 'Wireless Mouse & USB Hub',
      productId: '#90123',
      date: '01/03/2023',
      amount: '158 TND',
      paymentMode: 'Cash',
      status: 'New',
      productImage: 'https://placehold.co/24x24',
      rating: 4.3,
      brandName: 'Zen',
      brandLogo: 'assets/images/brands/zen.png',
      products: [
        { name: 'Wireless Mouse', price: '89 TND' },
        { name: 'USB Hub', price: '69 TND' }
      ]
    },
    { ticketNumber: 19, productName: 'External SSD', productId: '#45678', date: '14/03/2023', amount: '399 TND', paymentMode: 'Transfer Bank', status: 'Loyal', productImage: 'https://placehold.co/24x24', rating: 4.9, brandName: 'Monoprix', brandLogo: 'assets/images/brands/monoprix.png' },
    { ticketNumber: 20, productName: 'Webcam', productId: '#89012', date: '28/03/2023', amount: '149 TND', paymentMode: 'Cash on Delivery', status: 'New', productImage: 'https://placehold.co/24x24', rating: 4.0, brandName: 'Aziza', brandLogo: 'assets/images/brands/aziza.png' },
    {
      ticketNumber: 21,
      productName: 'Gaming Keyboard, Mouse & Headset',
      productId: '#34567',
      date: '10/04/2023',
      amount: '607.95 TND',
      paymentMode: 'Transfer Bank',
      status: 'In progress',
      productImage: 'https://placehold.co/24x24',
      rating: 4.7,
      brandName: 'Carrefour',
      brandLogo: 'assets/images/brands/carrefour.png',
      products: [
        { name: 'Gaming Keyboard', price: '289 TND' },
        { name: 'Gaming Mouse', price: '89.95 TND' },
        { name: 'Headset', price: '229 TND' }
      ]
    },
    { ticketNumber: 22, productName: 'Monitor Stand', productId: '#78901', date: '24/04/2023', amount: '79 TND', paymentMode: 'Cash', status: 'New', productImage: 'https://placehold.co/24x24', rating: 3.6, brandName: 'Zen', brandLogo: 'assets/images/brands/zen.png' },
    { ticketNumber: 23, productName: 'USB Hub', productId: '#23456', date: '08/05/2023', amount: '69 TND', paymentMode: 'Cash on Delivery', status: 'Loyal', productImage: 'https://placehold.co/24x24', rating: 4.1, brandName: 'Monoprix', brandLogo: 'assets/images/brands/monoprix.png' },
    {
      ticketNumber: 24,
      productName: 'Microphone & Webcam',
      productId: '#67890',
      date: '22/05/2023',
      amount: '348 TND',
      paymentMode: 'Transfer Bank',
      status: 'New',
      productImage: 'https://placehold.co/24x24',
      rating: 4.4,
      brandName: 'Aziza',
      brandLogo: 'assets/images/brands/aziza.png',
      products: [
        { name: 'Microphone', price: '199 TND' },
        { name: 'Webcam', price: '149 TND' }
      ]
    },
    { ticketNumber: 25, productName: 'Router', productId: '#12345', date: '05/06/2023', amount: '329 TND', paymentMode: 'Cash', status: 'In progress', productImage: 'https://placehold.co/24x24', rating: 3.9, brandName: 'Carrefour', brandLogo: 'assets/images/brands/carrefour.png' },
    { ticketNumber: 26, productName: 'Power Bank', productId: '#56789', date: '19/06/2023', amount: '119 TND', paymentMode: 'Transfer Bank', status: 'New', productImage: 'https://placehold.co/24x24', rating: 4.2, brandName: 'Zen', brandLogo: 'assets/images/brands/zen.png' },
    {
      ticketNumber: 27,
      productName: 'Desk Mat & Cable Organizer',
      productId: '#90123',
      date: '03/07/2023',
      amount: '88 TND',
      paymentMode: 'Cash on Delivery',
      status: 'Loyal',
      productImage: 'https://placehold.co/24x24',
      rating: 4.5,
      brandName: 'Monoprix',
      brandLogo: 'assets/images/brands/monoprix.png',
      products: [
        { name: 'Desk Mat', price: '49 TND' },
        { name: 'Cable Organizer', price: '39 TND' }
      ]
    },
    { ticketNumber: 28, productName: 'Cable Organizer', productId: '#45678', date: '17/07/2023', amount: '39 TND', paymentMode: 'Cash', status: 'New', productImage: 'https://placehold.co/24x24', rating: 3.7, brandName: 'Aziza', brandLogo: 'assets/images/brands/aziza.png' },
    { ticketNumber: 29, productName: 'Laptop Stand', productId: '#89012', date: '31/07/2023', amount: '159 TND', paymentMode: 'Transfer Bank', status: 'In progress', productImage: 'https://placehold.co/24x24', rating: 4.3, brandName: 'Carrefour', brandLogo: 'assets/images/brands/carrefour.png' },
    {
      ticketNumber: 30,
      productName: 'Wireless Charger & Portable Charger',
      productId: '#34567',
      date: '14/08/2023',
      amount: '188 TND',
      paymentMode: 'Cash on Delivery',
      status: 'New',
      productImage: 'https://placehold.co/24x24',
      rating: 4.0,
      brandName: 'Zen',
      brandLogo: 'assets/images/brands/zen.png',
      products: [
        { name: 'Wireless Charger', price: '89 TND' },
        { name: 'Portable Charger', price: '99 TND' }
      ]
    },
    { ticketNumber: 31, productName: 'Backpack & Water Bottle', productId: '#98765', date: '28/08/2023', amount: '129 TND', products: [{ name: 'Backpack', price: '99 TND' }, { name: 'Water Bottle', price: '30 TND' }], paymentMode: 'Cash', status: 'Loyal', productImage: 'https://placehold.co/24x24', rating: 4.2, brandName: 'Carrefour', brandLogo: 'assets/images/brands/carrefour.png' },
    { ticketNumber: 32, productName: 'Tablet', productId: '#54321', date: '10/09/2023', amount: '799 TND', paymentMode: 'Transfer Bank', status: 'New', productImage: 'https://placehold.co/24x24', rating: 4.6, brandName: 'Zen', brandLogo: 'assets/images/brands/zen.png' },
    {
      ticketNumber: 33,
      productName: 'Sunglasses & Cap',
      productId: '#11223',
      date: '22/09/2023',
      amount: '128 TND',
      paymentMode: 'Cash on Delivery',
      status: 'In progress',
      productImage: 'https://placehold.co/24x24',
      rating: 3.9,
      brandName: 'Monoprix',
      brandLogo: 'assets/images/brands/monoprix.png',
      products: [
        { name: 'Sunglasses', price: '89 TND' },
        { name: 'Cap', price: '39 TND' }
      ]
    },
    { ticketNumber: 34, productName: 'Jacket & Scarf', productId: '#44556', date: '05/10/2023', amount: '249 TND', products: [{ name: 'Jacket', price: '199 TND' }, { name: 'Scarf', price: '50 TND' }], paymentMode: 'Cash', status: 'New', productImage: 'https://placehold.co/24x24', rating: 4.3, brandName: 'Aziza', brandLogo: 'assets/images/brands/aziza.png' },
    { ticketNumber: 35, productName: 'Smart TV', productId: '#77889', date: '18/10/2023', amount: '1499 TND', paymentMode: 'Transfer Bank', status: 'Loyal', productImage: 'https://placehold.co/24x24', rating: 4.7, brandName: 'Carrefour', brandLogo: 'assets/images/brands/carrefour.png' },
    {
      ticketNumber: 36,
      productName: 'Earphones & Power Bank',
      productId: '#99001',
      date: '01/11/2023',
      amount: '198 TND',
      paymentMode: 'Cash on Delivery',
      status: 'New',
      productImage: 'https://placehold.co/24x24',
      rating: 4.1,
      brandName: 'Zen',
      brandLogo: 'assets/images/brands/zen.png',
      products: [
        { name: 'Earphones', price: '79 TND' },
        { name: 'Power Bank', price: '119 TND' }
      ]
    },
    { ticketNumber: 37, productName: 'Desk & Chair', productId: '#22334', date: '14/11/2023', amount: '499 TND', products: [{ name: 'Desk', price: '349 TND' }, { name: 'Chair', price: '150 TND' }], paymentMode: 'Cash', status: 'In progress', productImage: 'https://placehold.co/24x24', rating: 4.4, brandName: 'Monoprix', brandLogo: 'assets/images/brands/monoprix.png' },
    { ticketNumber: 38, productName: 'Sneakers', productId: '#55667', date: '27/11/2023', amount: '199 TND', paymentMode: 'Transfer Bank', status: 'New', productImage: 'https://placehold.co/24x24', rating: 4.0, brandName: 'Aziza', brandLogo: 'assets/images/brands/aziza.png' },
    {
      ticketNumber: 39,
      productName: 'Projector & Screen',
      productId: '#88990',
      date: '10/12/2023',
      amount: '848 TND',
      paymentMode: 'Cash on Delivery',
      status: 'Loyal',
      productImage: 'https://placehold.co/24x24',
      rating: 4.5,
      brandName: 'Carrefour',
      brandLogo: 'assets/images/brands/carrefour.png',
      products: [
        { name: 'Projector', price: '699 TND' },
        { name: 'Screen', price: '149 TND' }
      ]
    },
    { ticketNumber: 40, productName: 'Mouse Pad & USB Drive', productId: '#33445', date: '23/12/2023', amount: '59 TND', products: [{ name: 'Mouse Pad', price: '29 TND' }, { name: 'USB Drive', price: '30 TND' }], paymentMode: 'Cash', status: 'New', productImage: 'https://placehold.co/24x24', rating: 3.8, brandName: 'Zen', brandLogo: 'assets/images/brands/zen.png' },
    { ticketNumber: 41, productName: 'Toaster', productId: '#66778', date: '05/01/2024', amount: '99 TND', paymentMode: 'Transfer Bank', status: 'In progress', productImage: 'https://placehold.co/24x24', rating: 4.2, brandName: 'Monoprix', brandLogo: 'assets/images/brands/monoprix.png' },
    {
      ticketNumber: 42,
      productName: 'Wallet & Keychain',
      productId: '#99012',
      date: '18/01/2024',
      amount: '88 TND',
      paymentMode: 'Cash on Delivery',
      status: 'New',
      productImage: 'https://placehold.co/24x24',
      rating: 3.7,
      brandName: 'Aziza',
      brandLogo: 'assets/images/brands/aziza.png',
      products: [
        { name: 'Wallet', price: '69 TND' },
        { name: 'Keychain', price: '19 TND' }
      ]
    },
    { ticketNumber: 43, productName: 'Electric Kettle & Mug', productId: '#22345', date: '31/01/2024', amount: '129 TND', products: [{ name: 'Electric Kettle', price: '99 TND' }, { name: 'Mug', price: '30 TND' }], paymentMode: 'Cash', status: 'Loyal', productImage: 'https://placehold.co/24x24', rating: 4.3, brandName: 'Carrefour', brandLogo: 'assets/images/brands/carrefour.png' },
    { ticketNumber: 44, productName: 'Fitness Tracker', productId: '#55678', date: '13/02/2024', amount: '249 TND', paymentMode: 'Transfer Bank', status: 'New', productImage: 'https://placehold.co/24x24', rating: 4.1, brandName: 'Zen', brandLogo: 'assets/images/brands/zen.png' },
    {
      ticketNumber: 45,
      productName: 'Blender & Juicer',
      productId: '#88901',
      date: '26/02/2024',
      amount: '418 TND',
      paymentMode: 'Cash on Delivery',
      status: 'In progress',
      productImage: 'https://placehold.co/24x24',
      rating: 4.4,
      brandName: 'Monoprix',
      brandLogo: 'assets/images/brands/monoprix.png',
      products: [
        { name: 'Blender', price: '179 TND' },
        { name: 'Juicer', price: '239 TND' }
      ]
    },
    { ticketNumber: 46, productName: 'Hoodie & Cap', productId: '#33456', date: '10/03/2024', amount: '149 TND', products: [{ name: 'Hoodie', price: '109 TND' }, { name: 'Cap', price: '40 TND' }], paymentMode: 'Cash', status: 'New', productImage: 'https://placehold.co/24x24', rating: 3.9, brandName: 'Aziza', brandLogo: 'assets/images/brands/aziza.png' },
    { ticketNumber: 47, productName: 'Vacuum Cleaner', productId: '#66789', date: '23/03/2024', amount: '399 TND', paymentMode: 'Transfer Bank', status: 'Loyal', productImage: 'https://placehold.co/24x24', rating: 4.6, brandName: 'Carrefour', brandLogo: 'assets/images/brands/carrefour.png' },
    {
      ticketNumber: 48,
      productName: 'Wireless Earbuds & Case',
      productId: '#99023',
      date: '05/04/2024',
      amount: '189 TND',
      paymentMode: 'Cash on Delivery',
      status: 'New',
      productImage: 'https://placehold.co/24x24',
      rating: 4.2,
      brandName: 'Zen',
      brandLogo: 'assets/images/brands/zen.png',
      products: [
        { name: 'Wireless Earbuds', price: '159 TND' },
        { name: 'Case', price: '30 TND' }
      ]
    },
    { ticketNumber: 49, productName: 'Bookshelf & Lamp', productId: '#22356', date: '18/04/2024', amount: '299 TND', products: [{ name: 'Bookshelf', price: '249 TND' }, { name: 'Lamp', price: '50 TND' }], paymentMode: 'Cash', status: 'In progress', productImage: 'https://placehold.co/24x24', rating: 4.5, brandName: 'Monoprix', brandLogo: 'assets/images/brands/monoprix.png' },
    { ticketNumber: 50, productName: 'Socks', productId: '#55689', date: '01/05/2024', amount: '29 TND', paymentMode: 'Transfer Bank', status: 'New', productImage: 'https://placehold.co/24x24', rating: 3.8, brandName: 'Aziza', brandLogo: 'assets/images/brands/aziza.png' },
    {
      ticketNumber: 51,
      productName: 'Microwave Oven & Cookbook',
      productId: '#88912',
      date: '14/05/2024',
      amount: '289 TND',
      paymentMode: 'Cash on Delivery',
      status: 'Loyal',
      productImage: 'https://placehold.co/24x24',
      rating: 4.3,
      brandName: 'Carrefour',
      brandLogo: 'assets/images/brands/carrefour.png',
      products: [
        { name: 'Microwave Oven', price: '249 TND' },
        { name: 'Cookbook', price: '40 TND' }
      ]
    },
    { ticketNumber: 52, productName: 'Mouse & Keyboard', productId: '#33467', date: '27/05/2024', amount: '139 TND', products: [{ name: 'Mouse', price: '49 TND' }, { name: 'Keyboard', price: '90 TND' }], paymentMode: 'Cash', status: 'New', productImage: 'https://placehold.co/24x24', rating: 4.0, brandName: 'Zen', brandLogo: 'assets/images/brands/zen.png' },
    { ticketNumber: 53, productName: 'Electric Fan', productId: '#66790', date: '09/06/2024', amount: '119 TND', paymentMode: 'Transfer Bank', status: 'In progress', productImage: 'https://placehold.co/24x24', rating: 4.1, brandName: 'Monoprix', brandLogo: 'assets/images/brands/monoprix.png' },
    {
      ticketNumber: 54,
      productName: 'Belt & Wallet',
      productId: '#99034',
      date: '22/06/2024',
      amount: '128 TND',
      paymentMode: 'Cash on Delivery',
      status: 'New',
      productImage: 'https://placehold.co/24x24',
      rating: 3.7,
      brandName: 'Aziza',
      brandLogo: 'assets/images/brands/aziza.png',
      products: [
        { name: 'Belt', price: '59 TND' },
        { name: 'Wallet', price: '69 TND' }
      ]
    },
    { ticketNumber: 55, productName: 'Air Fryer & Cookbook', productId: '#22367', date: '05/07/2024', amount: '279 TND', products: [{ name: 'Air Fryer', price: '239 TND' }, { name: 'Cookbook', price: '40 TND' }], paymentMode: 'Cash', status: 'Loyal', productImage: 'https://placehold.co/24x24', rating: 4.4, brandName: 'Carrefour', brandLogo: 'assets/images/brands/carrefour.png' },
    { ticketNumber: 56, productName: 'Portable Charger', productId: '#55690', date: '18/07/2024', amount: '99 TND', paymentMode: 'Transfer Bank', status: 'New', productImage: 'https://placehold.co/24x24', rating: 4.2, brandName: 'Zen', brandLogo: 'assets/images/brands/zen.png' },
    {
      ticketNumber: 57,
      productName: 'Picture Frame & Wall Clock',
      productId: '#88923',
      date: '31/07/2024',
      amount: '128 TND',
      paymentMode: 'Cash on Delivery',
      status: 'In progress',
      productImage: 'https://placehold.co/24x24',
      rating: 3.9,
      brandName: 'Monoprix',
      brandLogo: 'assets/images/brands/monoprix.png',
      products: [
        { name: 'Picture Frame', price: '49 TND' },
        { name: 'Wall Clock', price: '79 TND' }
      ]
    },
    { ticketNumber: 58, productName: 'Jeans & Shirt', productId: '#33478', date: '13/08/2024', amount: '229 TND', products: [{ name: 'Jeans', price: '149 TND' }, { name: 'Shirt', price: '80 TND' }], paymentMode: 'Cash', status: 'New', productImage: 'https://placehold.co/24x24', rating: 4.3, brandName: 'Aziza', brandLogo: 'assets/images/brands/aziza.png' },
    { ticketNumber: 59, productName: 'Smart Doorbell', productId: '#66801', date: '26/08/2024', amount: '349 TND', paymentMode: 'Transfer Bank', status: 'Loyal', productImage: 'https://placehold.co/24x24', rating: 4.5, brandName: 'Carrefour', brandLogo: 'assets/images/brands/carrefour.png' },
    {
      ticketNumber: 60,
      productName: 'Headphones & Case',
      productId: '#99045',
      date: '08/09/2024',
      amount: '229 TND',
      paymentMode: 'Cash on Delivery',
      status: 'New',
      productImage: 'https://placehold.co/24x24',
      rating: 4.1,
      brandName: 'Zen',
      brandLogo: 'assets/images/brands/zen.png',
      products: [
        { name: 'Headphones', price: '199 TND' },
        { name: 'Case', price: '30 TND' }
      ]
    },
    { ticketNumber: 61, productName: 'Rug & Cushions', productId: '#22378', date: '21/09/2024', amount: '259 TND', products: [{ name: 'Rug', price: '199 TND' }, { name: 'Cushions', price: '60 TND' }], paymentMode: 'Cash', status: 'In progress', productImage: 'https://placehold.co/24x24', rating: 4.4, brandName: 'Monoprix', brandLogo: 'assets/images/brands/monoprix.png' },
    { ticketNumber: 62, productName: 'Umbrella', productId: '#55701', date: '04/10/2024', amount: '39 TND', paymentMode: 'Transfer Bank', status: 'New', productImage: 'https://placehold.co/24x24', rating: 3.8, brandName: 'Aziza', brandLogo: 'assets/images/brands/aziza.png' },
    {
      ticketNumber: 63,
      productName: 'Food Processor & Blender',
      productId: '#88934',
      date: '17/10/2024',
      amount: '508 TND',
      paymentMode: 'Cash on Delivery',
      status: 'Loyal',
      productImage: 'https://placehold.co/24x24',
      rating: 4.6,
      brandName: 'Carrefour',
      brandLogo: 'assets/images/brands/carrefour.png',
      products: [
        { name: 'Food Processor', price: '329 TND' },
        { name: 'Blender', price: '179 TND' }
      ]
    },
    { ticketNumber: 64, productName: 'Gaming Mouse', productId: '#33489', date: '30/10/2024', amount: '109 TND', paymentMode: 'Cash', status: 'New', productImage: 'https://placehold.co/24x24', rating: 4.2, brandName: 'Zen', brandLogo: 'assets/images/brands/zen.png' },
    { ticketNumber: 65, productName: 'Wall Clock', productId: '#66812', date: '12/11/2024', amount: '79 TND', paymentMode: 'Transfer Bank', status: 'In progress', productImage: 'https://placehold.co/24x24', rating: 3.9, brandName: 'Monoprix', brandLogo: 'assets/images/brands/monoprix.png' },
    {
      ticketNumber: 66,
      productName: 'Sweater, Gloves & Scarf',
      productId: '#99056',
      date: '25/11/2024',
      amount: '258 TND',
      paymentMode: 'Cash on Delivery',
      status: 'New',
      productImage: 'https://placehold.co/24x24',
      rating: 4.3,
      brandName: 'Aziza',
      brandLogo: 'assets/images/brands/aziza.png',
      products: [
        { name: 'Sweater', price: '149 TND' },
        { name: 'Gloves', price: '59 TND' },
        { name: 'Scarf', price: '50 TND' }
      ]
    },
    { ticketNumber: 67, productName: 'Robot Vacuum', productId: '#22389', date: '08/12/2024', amount: '599 TND', paymentMode: 'Cash', status: 'Loyal', productImage: 'https://placehold.co/24x24', rating: 4.7, brandName: 'Carrefour', brandLogo: 'assets/images/brands/carrefour.png' },
    { ticketNumber: 68, productName: 'Dash Cam', productId: '#55712', date: '21/12/2024', amount: '189 TND', paymentMode: 'Transfer Bank', status: 'New', productImage: 'https://placehold.co/24x24', rating: 4.0, brandName: 'Zen', brandLogo: 'assets/images/brands/zen.png' },
    {
      ticketNumber: 69,
      productName: 'Curtains & Tiebacks',
      productId: '#88945',
      date: '03/01/2025',
      amount: '179 TND',
      paymentMode: 'Cash on Delivery',
      status: 'In progress',
      productImage: 'https://placehold.co/24x24',
      rating: 4.2,
      brandName: 'Monoprix',
      brandLogo: 'assets/images/brands/monoprix.png',
      products: [
        { name: 'Curtains', price: '149 TND' },
        { name: 'Tiebacks', price: '30 TND' }
      ]
    },
    { ticketNumber: 70, productName: 'Watch & Bracelet', productId: '#33490', date: '16/01/2025', amount: '199 TND', products: [{ name: 'Watch', price: '149 TND' }, { name: 'Bracelet', price: '50 TND' }], paymentMode: 'Cash', status: 'New', productImage: 'https://placehold.co/24x24', rating: 4.1, brandName: 'Aziza', brandLogo: 'assets/images/brands/aziza.png' },
    { ticketNumber: 71, productName: 'Electric Toothbrush', productId: '#66823', date: '29/01/2025', amount: '129 TND', paymentMode: 'Transfer Bank', status: 'Loyal', productImage: 'https://placehold.co/24x24', rating: 4.4, brandName: 'Carrefour', brandLogo: 'assets/images/brands/carrefour.png' },
    {
      ticketNumber: 72,
      productName: 'Smart Plug & Light Bulb',
      productId: '#99067',
      date: '11/02/2025',
      amount: '158 TND',
      paymentMode: 'Cash on Delivery',
      status: 'New',
      productImage: 'https://placehold.co/24x24',
      rating: 3.8,
      brandName: 'Zen',
      brandLogo: 'assets/images/brands/zen.png',
      products: [
        { name: 'Smart Plug', price: '69 TND' },
        { name: 'Smart Light Bulb', price: '89 TND' }
      ]
    },
    { ticketNumber: 73, productName: 'Pillow & Blanket', productId: '#22390', date: '24/02/2025', amount: '179 TND', products: [{ name: 'Pillow', price: '79 TND' }, { name: 'Blanket', price: '100 TND' }], paymentMode: 'Cash', status: 'In progress', productImage: 'https://placehold.co/24x24', rating: 4.3, brandName: 'Monoprix', brandLogo: 'assets/images/brands/monoprix.png' },
    { ticketNumber: 74, productName: 'Keychain', productId: '#55723', date: '09/03/2025', amount: '19 TND', paymentMode: 'Transfer Bank', status: 'New', productImage: 'https://placehold.co/24x24', rating: 3.7, brandName: 'Aziza', brandLogo: 'assets/images/brands/aziza.png' },
    {
      ticketNumber: 75,
      productName: 'Juicer & Blender',
      productId: '#88956',
      date: '22/03/2025',
      amount: '418 TND',
      paymentMode: 'Cash on Delivery',
      status: 'Loyal',
      productImage: 'https://placehold.co/24x24',
      rating: 4.5,
      brandName: 'Carrefour',
      brandLogo: 'assets/images/brands/carrefour.png',
      products: [
        { name: 'Juicer', price: '239 TND' },
        { name: 'Blender', price: '179 TND' }
      ]
    },
    { ticketNumber: 76, productName: 'Laptop Bag', productId: '#33501', date: '04/04/2025', amount: '109 TND', paymentMode: 'Cash', status: 'New', productImage: 'https://placehold.co/24x24', rating: 4.0, brandName: 'Zen', brandLogo: 'assets/images/brands/zen.png' },
    { ticketNumber: 77, productName: 'Mirror', productId: '#66834', date: '17/04/2025', amount: '89 TND', paymentMode: 'Transfer Bank', status: 'In progress', productImage: 'https://placehold.co/24x24', rating: 4.2, brandName: 'Monoprix', brandLogo: 'assets/images/brands/monoprix.png' },
    {
      ticketNumber: 78,
      productName: 'Towel Set & Bath Mat',
      productId: '#99078',
      date: '30/04/2025',
      amount: '148 TND',
      paymentMode: 'Cash on Delivery',
      status: 'New',
      productImage: 'https://placehold.co/24x24',
      rating: 4.1,
      brandName: 'Aziza',
      brandLogo: 'assets/images/brands/aziza.png',
      products: [
        { name: 'Towel Set', price: '99 TND' },
        { name: 'Bath Mat', price: '49 TND' }
      ]
    },
    { ticketNumber: 79, productName: 'Hair Dryer & Brush', productId: '#22401', date: '13/05/2025', amount: '159 TND', products: [{ name: 'Hair Dryer', price: '119 TND' }, { name: 'Brush', price: '40 TND' }], paymentMode: 'Cash', status: 'Loyal', productImage: 'https://placehold.co/24x24', rating: 4.4, brandName: 'Carrefour', brandLogo: 'assets/images/brands/carrefour.png' },
    { ticketNumber: 80, productName: 'VR Headset', productId: '#55734', date: '26/05/2025', amount: '499 TND', paymentMode: 'Transfer Bank', status: 'New', productImage: 'https://placehold.co/24x24', rating: 4.6, brandName: 'Zen', brandLogo: 'assets/images/brands/zen.png' },
    {
      ticketNumber: 81,
      productName: 'Coat Rack & Shoe Rack',
      productId: '#88967',
      date: '08/06/2025',
      amount: '228 TND',
      paymentMode: 'Cash on Delivery',
      status: 'In progress',
      productImage: 'https://placehold.co/24x24',
      rating: 3.9,
      brandName: 'Monoprix',
      brandLogo: 'assets/images/brands/monoprix.png',
      products: [
        { name: 'Coat Rack', price: '129 TND' },
        { name: 'Shoe Rack', price: '99 TND' }
      ]
    },
    { ticketNumber: 82, productName: 'Necklace', productId: '#33512', date: '21/06/2025', amount: '149 TND', paymentMode: 'Cash', status: 'New', productImage: 'https://placehold.co/24x24', rating: 4.3, brandName: 'Aziza', brandLogo: 'assets/images/brands/aziza.png' },
    { ticketNumber: 83, productName: 'Smart Thermostat', productId: '#66845', date: '04/07/2025', amount: '299 TND', paymentMode: 'Transfer Bank', status: 'Loyal', productImage: 'https://placehold.co/24x24', rating: 4.5, brandName: 'Carrefour', brandLogo: 'assets/images/brands/carrefour.png' },
    {
      ticketNumber: 84,
      productName: 'Action Camera & Mount',
      productId: '#99089',
      date: '17/07/2025',
      amount: '319 TND',
      paymentMode: 'Cash on Delivery',
      status: 'New',
      productImage: 'https://placehold.co/24x24',
      rating: 4.2,
      brandName: 'Zen',
      brandLogo: 'assets/images/brands/zen.png',
      products: [
        { name: 'Action Camera', price: '279 TND' },
        { name: 'Mount', price: '40 TND' }
      ]
    },
    { ticketNumber: 85, productName: 'Table Lamp', productId: '#22412', date: '30/07/2025', amount: '79 TND', paymentMode: 'Cash', status: 'In progress', productImage: 'https://placehold.co/24x24', rating: 4.0, brandName: 'Monoprix', brandLogo: 'assets/images/brands/monoprix.png' },
    { ticketNumber: 86, productName: 'Sunglasses & Hat', productId: '#55745', date: '12/08/2025', amount: '139 TND', products: [{ name: 'Sunglasses', price: '89 TND' }, { name: 'Hat', price: '50 TND' }], paymentMode: 'Transfer Bank', status: 'New', productImage: 'https://placehold.co/24x24', rating: 4.1, brandName: 'Aziza', brandLogo: 'assets/images/brands/aziza.png' },
    { ticketNumber: 87, productName: 'Waffle Maker', productId: '#88978', date: '25/08/2025', amount: '169 TND', paymentMode: 'Cash on Delivery', status: 'Loyal', productImage: 'https://placehold.co/24x24', rating: 4.4, brandName: 'Carrefour', brandLogo: 'assets/images/brands/carrefour.png' },
    {
      ticketNumber: 88,
      productName: 'Smart Scale & Fitness Tracker',
      productId: '#33523',
      date: '07/09/2025',
      amount: '348 TND',
      paymentMode: 'Cash',
      status: 'New',
      productImage: 'https://placehold.co/24x24',
      rating: 4.0,
      brandName: 'Zen',
      brandLogo: 'assets/images/brands/zen.png',
      products: [
        { name: 'Smart Scale', price: '99 TND' },
        { name: 'Fitness Tracker', price: '249 TND' }
      ]
    },
    { ticketNumber: 89, productName: 'Storage Box', productId: '#66856', date: '20/09/2025', amount: '59 TND', paymentMode: 'Transfer Bank', status: 'In progress', productImage: 'https://placehold.co/24x24', rating: 3.8, brandName: 'Monoprix', brandLogo: 'assets/images/brands/monoprix.png' },
    { ticketNumber: 90, productName: 'Earrings', productId: '#99090', date: '03/10/2025', amount: '89 TND', paymentMode: 'Cash on Delivery', status: 'New', productImage: 'https://placehold.co/24x24', rating: 4.2, brandName: 'Aziza', brandLogo: 'assets/images/brands/aziza.png' },
    {
      ticketNumber: 91,
      productName: 'Pressure Cooker & Cookware Set',
      productId: '#22423',
      date: '16/10/2025',
      amount: '419 TND',
      paymentMode: 'Cash',
      status: 'Loyal',
      productImage: 'https://placehold.co/24x24',
      rating: 4.5,
      brandName: 'Carrefour',
      brandLogo: 'assets/images/brands/carrefour.png',
      products: [
        { name: 'Pressure Cooker', price: '269 TND' },
        { name: 'Cookware Set', price: '150 TND' }
      ]
    },
    { ticketNumber: 92, productName: 'Drone', productId: '#55756', date: '29/10/2025', amount: '599 TND', paymentMode: 'Transfer Bank', status: 'New', productImage: 'https://placehold.co/24x24', rating: 4.6, brandName: 'Zen', brandLogo: 'assets/images/brands/zen.png' },
    { ticketNumber: 93, productName: 'Plant Pot', productId: '#88989', date: '11/11/2025', amount: '49 TND', paymentMode: 'Cash on Delivery', status: 'In progress', productImage: 'https://placehold.co/24x24', rating: 3.9, brandName: 'Monoprix', brandLogo: 'assets/images/brands/monoprix.png' },
    {
      ticketNumber: 94,
      productName: 'Scarf, Mittens & Hat',
      productId: '#33534',
      date: '24/11/2025',
      amount: '209 TND',
      paymentMode: 'Cash',
      status: 'New',
      productImage: 'https://placehold.co/24x24',
      rating: 4.1,
      brandName: 'Aziza',
      brandLogo: 'assets/images/brands/aziza.png',
      products: [
        { name: 'Scarf', price: '50 TND' },
        { name: 'Mittens', price: '59 TND' },
        { name: 'Hat', price: '100 TND' }
      ]
    },
    { ticketNumber: 95, productName: 'Electric Grill', productId: '#66867', date: '07/12/2025', amount: '349 TND', paymentMode: 'Transfer Bank', status: 'Loyal', productImage: 'https://placehold.co/24x24', rating: 4.4, brandName: 'Carrefour', brandLogo: 'assets/images/brands/carrefour.png' },
    { ticketNumber: 96, productName: 'Smart Speaker', productId: '#99101', date: '20/12/2025', amount: '199 TND', paymentMode: 'Cash on Delivery', status: 'New', productImage: 'https://placehold.co/24x24', rating: 4.3, brandName: 'Zen', brandLogo: 'assets/images/brands/zen.png' },
    {
      ticketNumber: 97,
      productName: 'Photo Album & Frame',
      productId: '#22434',
      date: '02/01/2026',
      amount: '118 TND',
      paymentMode: 'Cash',
      status: 'In progress',
      productImage: 'https://placehold.co/24x24',
      rating: 4.0,
      brandName: 'Monoprix',
      brandLogo: 'assets/images/brands/monoprix.png',
      products: [
        { name: 'Photo Album', price: '69 TND' },
        { name: 'Frame', price: '49 TND' }
      ]
    },
    { ticketNumber: 98, productName: 'Ring', productId: '#55767', date: '15/01/2026', amount: '179 TND', paymentMode: 'Transfer Bank', status: 'New', productImage: 'https://placehold.co/24x24', rating: 4.2, brandName: 'Aziza', brandLogo: 'assets/images/brands/aziza.png' },
    { ticketNumber: 99, productName: 'Rice Cooker & Spatula', productId: '#88990', date: '28/01/2026', amount: '229 TND', products: [{ name: 'Rice Cooker', price: '199 TND' }, { name: 'Spatula', price: '30 TND' }], paymentMode: 'Cash on Delivery', status: 'Loyal', productImage: 'https://placehold.co/24x24', rating: 4.5, brandName: 'Carrefour', brandLogo: 'assets/images/brands/carrefour.png' },
    {
      ticketNumber: 100,
      productName: 'Smart Light Bulb & Smart Plug',
      productId: '#33545',
      date: '10/02/2026',
      amount: '158 TND',
      paymentMode: 'Cash',
      status: 'New',
      productImage: 'https://placehold.co/24x24',
      rating: 4.1,
      brandName: 'Zen',
      brandLogo: 'assets/images/brands/zen.png',
      products: [
        { name: 'Smart Light Bulb', price: '89 TND' },
        { name: 'Smart Plug', price: '69 TND' }
      ]
    }
  ];

  // Working copy of transaction data that can be modified
  transactions: any[] = [];

  // UI state
  entriesPerPage: number = 10; // Default value
  currentPage: number = 1;
  searchQuery: string = '';
  displayedTransactions: any[] = [];
  selectedTickets: Set<number> = new Set();
  filteredTransactions: any[] = []; // Cache for filtered transactions
  isMobile: boolean = false;
  private destroy$ = new Subject<void>();
  private readonly mobileBreakpoint = 768;
  private statusClassCache = new Map<string, string>();
  showAddTicketModal = false;
  showBudgetModal = false;
  selectedTicketsAmount = 0;
  showSelectAllDropdown = false;

  constructor(
    private router: Router,
    private cdr: ChangeDetectorRef,
    private transactionService: TransactionService,
    private budgetDataService: BudgetDataService,
    private notificationService: NotificationService
  ) {
    // Initial check for mobile
    this.isMobile = window.innerWidth <= this.mobileBreakpoint;
  }

  ngOnInit() {
    // Validate the uniqueness of ticket numbers in the original data
    this.validateTicketNumbers();

    // Initialize transactions with a deep copy of the original data
    this.resetTransactionsToOriginal();

    // Load saved transactions from localStorage (only for newly added tickets, not for deletions)
    this.loadTransactionsFromLocalStorage();

    // Initialize with first page of items
    this.filterTransactions();

    // Setup optimized resize listener with debounce
    fromEvent(window, 'resize')
      .pipe(
        debounceTime(100), // 100ms debounce time
        takeUntil(this.destroy$)
      )
      .subscribe(() => {
        const wasAlreadyMobile = this.isMobile;
        this.isMobile = window.innerWidth <= this.mobileBreakpoint;

        // Only mark for check if state actually changed
        if (wasAlreadyMobile !== this.isMobile) {
          this.cdr.markForCheck();
        }
      });

    // Add event listener for page refresh/unload to reset data
    window.addEventListener('beforeunload', () => {
      this.resetTransactionsToOriginal();
    });
  }

  /**
   * Validate that all ticket numbers and productIds in the original data are unique
   * This helps prevent selection issues
   */
  private validateTicketNumbers(): void {
    const ticketNumbers = new Set<number>();
    const productIds = new Map<string, number[]>();
    const ticketDuplicates: number[] = [];
    const productIdDuplicates: string[] = [];

    this.originalTransactions.forEach(transaction => {
      // Check for duplicate ticket numbers
      if (ticketNumbers.has(transaction.ticketNumber)) {
        ticketDuplicates.push(transaction.ticketNumber);
      } else {
        ticketNumbers.add(transaction.ticketNumber);
      }

      // Check for duplicate productIds
      if (!productIds.has(transaction.productId)) {
        productIds.set(transaction.productId, [transaction.ticketNumber]);
      } else {
        const existingTickets = productIds.get(transaction.productId) || [];
        existingTickets.push(transaction.ticketNumber);
        productIds.set(transaction.productId, existingTickets);

        if (!productIdDuplicates.includes(transaction.productId)) {
          productIdDuplicates.push(transaction.productId);
        }
      }
    });

    if (ticketDuplicates.length > 0) {
      console.warn('Duplicate ticket numbers found:', ticketDuplicates);
    }

    if (productIdDuplicates.length > 0) {
      console.warn('Duplicate productIds found:', productIdDuplicates);

      // Log the ticket numbers associated with each duplicate productId
      productIdDuplicates.forEach(productId => {
        const tickets = productIds.get(productId);
        console.warn(`ProductId ${productId} is used by tickets:`, tickets);
      });
    }
  }

  // Track items in ngFor for better performance
  trackByFn(_index: number, item: any): number {
    // Ensure we're using the unique ticketNumber for tracking
    return item.ticketNumber;
  }

  updateEntries() {
    this.currentPage = 1;
    this.updateDisplayedTransactions();
    this.cdr.markForCheck();
  }

  filterTransactions() {
    // Apply search filter
    if (!this.searchQuery || this.searchQuery.trim() === '') {
      this.filteredTransactions = [...this.transactions];
    } else {
      const query = this.searchQuery.toLowerCase().trim();
      this.filteredTransactions = this.transactions.filter(transaction => {
        return (
          transaction.productName.toLowerCase().includes(query) ||
          transaction.brandName.toLowerCase().includes(query)
        );
      });
    }

    this.currentPage = 1;
    this.updateDisplayedTransactions();
    this.cdr.markForCheck();
  }

  updateDisplayedTransactions() {
    const startIndex = (this.currentPage - 1) * this.entriesPerPage;
    const endIndex = startIndex + this.entriesPerPage;
    this.displayedTransactions = this.filteredTransactions.slice(startIndex, endIndex);
  }

  totalPages() {
    return Math.ceil(this.filteredTransactions.length / this.entriesPerPage);
  }

  getPageNumbers() {
    const total = this.totalPages();
    const pages: number[] = [];
    const maxVisiblePages = 5;
    let startPage = Math.max(1, this.currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(total, startPage + maxVisiblePages - 1);

    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    return pages;
  }

  previousPage() {
    if (this.currentPage > 1) {
      this.currentPage--;
      this.updateDisplayedTransactions();
      this.cdr.markForCheck();
    }
  }

  nextPage() {
    if (this.currentPage < this.totalPages()) {
      this.currentPage++;
      this.updateDisplayedTransactions();
      this.cdr.markForCheck();
    }
  }

  goToPage(page: number) {
    if (page >= 1 && page <= this.totalPages()) {
      this.currentPage = page;
      this.updateDisplayedTransactions();
      this.cdr.markForCheck();
    }
  }

  getStatusClass(status: string) {
    const lowerStatus = status.toLowerCase();

    if (this.statusClassCache.has(lowerStatus)) {
      return this.statusClassCache.get(lowerStatus);
    }

    let result = '';
    switch (lowerStatus) {
      case 'loyal':
        result = 'status-loyal';
        break;
      case 'new':
        result = 'status-new';
        break;
      case 'in progress':
        result = 'status-in-progress';
        break;
    }

    this.statusClassCache.set(lowerStatus, result);
    return result;
  }

  toggleTicketSelection(ticketNumber: number, event: MouseEvent) {
    // Stop event propagation to prevent the row click handler from firing
    event.stopPropagation();

    // Ensure we're working with the correct ticket by finding it in the transactions array
    const transaction = this.transactions.find(t => t.ticketNumber === ticketNumber);

    if (!transaction) {
      console.error(`Transaction with ticket number ${ticketNumber} not found`);
      return;
    }

    // Toggle the selection state
    if (this.selectedTickets.has(ticketNumber)) {
      this.selectedTickets.delete(ticketNumber);
    } else {
      this.selectedTickets.add(ticketNumber);
    }

    this.cdr.markForCheck();
  }

  deleteSelectedTickets() {
    if (this.selectedTickets.size === 0) return;

    if (confirm(`Delete ${this.selectedTickets.size} selected item(s)?`)) {
      this.transactions = this.transactions.filter(t => !this.selectedTickets.has(t.ticketNumber));
      this.selectedTickets.clear();

      // We don't save to localStorage to ensure data is restored on page refresh
      // this.saveTransactionsToLocalStorage();

      this.filterTransactions();
    }
  }

  /**
   * Delete all filtered tickets (across all pages, not just the current page)
   */
  deleteAllDisplayedTickets() {
    if (this.filteredTransactions.length === 0) return;

    const confirmMessage = this.searchQuery ?
      `Delete all ${this.filteredTransactions.length} tickets matching "${this.searchQuery}"?` :
      `Delete all ${this.filteredTransactions.length} tickets?`;

    if (confirm(confirmMessage)) {
      // Get all ticket numbers from filtered transactions (across all pages)
      const ticketNumbersToDelete = new Set(this.filteredTransactions.map(t => t.ticketNumber));

      // Filter out the tickets to delete
      this.transactions = this.transactions.filter(t => !ticketNumbersToDelete.has(t.ticketNumber));

      // Clear any selected tickets that might have been deleted
      this.selectedTickets = new Set([...this.selectedTickets].filter(id => !ticketNumbersToDelete.has(id)));

      // We don't save to localStorage to ensure data is restored on page refresh
      // this.saveTransactionsToLocalStorage();

      // Update displayed transactions
      this.filterTransactions();

      // Notify user
      this.notificationService.addNotification({
        title: 'Tickets Deleted',
        message: `Successfully deleted ${ticketNumbersToDelete.size} tickets`,
        read: false,
        time: new Date()
      });
    }
  }

  viewTicketDetails(transaction: any, event: MouseEvent) {
    // Prevent navigation when clicking checkbox or within the select column
    if (event?.target instanceof HTMLElement &&
        (event.target.classList.contains('fas') || event.target.closest('.select-column'))) {
      return;
    }

    // Set the current transaction in the service before navigating
    this.transactionService.setCurrentTransaction(transaction);

    // Navigate to the ticket details page
    this.router.navigate(['/tickets', transaction.ticketNumber]);
  }

  getProductQuantity(transaction: any): number {
    // If the product name contains a comma or '&', it's multiple different products
    if (transaction.productName.includes(',') || transaction.productName.includes('&')) {
      return 1;
    }

    // Check if there are multiple quantities of the same item
    const quantity = transaction.quantity || 1;
    return quantity > 1 ? quantity : 0;
  }

  /**
   * Calculate the total amount of selected tickets
   * @returns The total amount in TND
   */
  calculateSelectedTicketsAmount(): number {
    let total = 0;

    // Get all selected transactions by ticket number
    const selectedTransactions = this.transactions.filter(t =>
      this.selectedTickets.has(t.ticketNumber)
    );

    // Calculate total amount
    selectedTransactions.forEach(transaction => {
      // Extract the numeric value from the amount string (e.g., "16 TND" -> 16)
      const amountStr = transaction.amount.replace(/[^\d.-]/g, '');
      const amount = parseFloat(amountStr);
      if (!isNaN(amount)) {
        total += amount;
      }
    });

    return total;
  }

  /**
   * Open the budget selection modal to add selected tickets to a budget
   */
  addToBudget() {
    if (this.selectedTickets.size === 0) return;

    // Calculate the total amount of selected tickets
    this.selectedTicketsAmount = this.calculateSelectedTicketsAmount();

    // Show the budget selection modal
    this.showBudgetModal = true;
    this.cdr.markForCheck();
  }

  /**
   * Handle the budget selection from the modal
   * @param event The budget selection event containing budgetId and amount
   */
  handleBudgetSelection(event: { budgetId: string, amount: number }) {
    // Add the tickets to the selected budget using the signal service
    this.budgetDataService.addTicketsToBudget(event.budgetId, event.amount).subscribe({
      next: (updatedBudget) => {
        if (updatedBudget) {
          // Show a notification
          this.notificationService.addNotification({
            title: 'Budget Updated',
            message: `Added ${event.amount} TND to ${updatedBudget.name}`,
            read: false,
            time: new Date()
          });

          // Clear the selected tickets
          this.selectedTickets.clear();

          // Close the modal
          this.showBudgetModal = false;

          // Update the UI
          this.cdr.markForCheck();
        }
      },
      error: (error) => {
        console.error('Error adding tickets to budget:', error);
        alert('Failed to add tickets to budget. Please try again.');
      }
    });
  }

  async exportSelectedTickets() {
    if (this.selectedTickets.size === 0) return;

    const pdf = new jsPDF('p', 'mm', 'a4');
    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();
    const margin = 10; // margin in mm
    let currentY = margin;

    // Get all selected transactions
    const selectedTransactions = this.transactions.filter(t => this.selectedTickets.has(t.ticketNumber));

    for (let i = 0; i < selectedTransactions.length; i++) {
      const transaction = selectedTransactions[i];

      // Create the receipt card element
      const receiptCard = document.createElement('div');
      receiptCard.className = 'receipt-card';
      receiptCard.style.width = '210mm'; // A4 width
      receiptCard.style.background = 'white';
      receiptCard.style.padding = '20px';
      receiptCard.style.boxSizing = 'border-box';

      // Calculate VAT and other values
      const totalAmount = parseFloat(transaction.amount.replace(/[^\d.-]/g, ''));
      const vatRate = 19;
      const vatAmount = totalAmount * (vatRate / 100);
      const subtotal = totalAmount - vatAmount;

      receiptCard.innerHTML = `
        <div class="receipt-header" style="text-align: center; margin-bottom: 20px; border-bottom: 1px solid #e5e7eb; padding-bottom: 15px;">
          <div class="merchant-info" style="display: flex; flex-direction: column; align-items: center;">
            <img src="${transaction.brandLogo}" alt="${transaction.brandName}" style="width: 96px; height: 96px; object-fit: contain; margin-bottom: 10px;">
            <h2 style="font-size: 24px; margin: 10px 0;">${transaction.brandName}</h2>
            <p style="margin: 5px 0; color: #6b7280;">Rue Yacer Arafat,Sahloul,Sousse, TN, 4021</p>
            <p style="margin: 5px 0; color: #6b7280;">Phone Number: +216 52 533 533</p>
          </div>
          <div class="rating" style="margin-top: 10px;">
            ${transaction.rating ? `
              <div class="stars" style="color: #f59e0b; font-size: 18px;">
                ${'★'.repeat(Math.floor(transaction.rating))}${transaction.rating % 1 >= 0.5 ? '½' : ''}${'☆'.repeat(5 - Math.ceil(transaction.rating))}
              </div>
              <span style="color: #4b5563;">${transaction.rating}/5</span>
            ` : ''}
          </div>
        </div>

        <div class="receipt-details" style="margin-top: 20px;">
          <div style="margin-bottom: 15px;">
            <p style="margin: 5px 0;">Receipt No.: ${transaction.ticketNumber}</p>
            <p style="margin: 5px 0;">Product ID: ${transaction.productId}</p>
            <p style="margin: 5px 0;">Date: ${transaction.date}</p>
            <p style="margin: 5px 0;">Status: <span style="color: ${transaction.status === 'Completed' ? '#10b981' : transaction.status === 'Pending' ? '#f59e0b' : '#ef4444'};">${transaction.status}</span></p>
          </div>

          <div class="receipt-items" style="margin: 20px 0; border-bottom: 1px solid #e5e7eb;">
            <div style="padding: 10px 0; border-bottom: 1px solid #e5e7eb;">
              <div style="display: flex; justify-content: space-between; align-items: center;">
                <span style="flex: 1;">${transaction.productName}</span>
                <div style="display: flex; gap: 20px; min-width: 200px;">
                  <span>x1</span>
                  <span>${transaction.amount}</span>
                </div>
              </div>
            </div>
          </div>

          <div style="margin-top: 20px; background: #f9fafb; padding: 15px; border-radius: 8px;">
            <div style="display: flex; justify-content: space-between; margin: 5px 0;">
              <span>Subtotal:</span>
              <span>${subtotal.toFixed(2)} TND</span>
            </div>
            <div style="display: flex; justify-content: space-between; margin: 5px 0;">
              <span>VAT (${vatRate}%):</span>
              <span>${vatAmount.toFixed(2)} TND</span>
            </div>
            <div style="display: flex; justify-content: space-between; margin: 5px 0; font-weight: bold; border-top: 2px solid #e5e7eb; padding-top: 10px;">
              <span>Total:</span>
              <span>${totalAmount.toFixed(2)} TND</span>
            </div>
          </div>

          <div style="display: flex; justify-content: space-between; margin-top: 20px; color: #6b7280;">
            <div>
              <div>Payment Method</div>
              <div style="margin-top: 5px; color: #1f2937;">
                ${transaction.paymentMode}
                <i class="fas ${
                  transaction.paymentMode === 'Credit Card' ? 'fa-credit-card' :
                  transaction.paymentMode === 'Cash' ? 'fa-money-bill-wave' :
                  transaction.paymentMode === 'Transfer Bank' ? 'fa-university' :
                  transaction.paymentMode === 'PayPal' ? 'fa-paypal' :
                  'fa-money-check-alt'
                }" style="margin-left: 5px;"></i>
              </div>
            </div>
            <div>
              <div>Number Of Articles</div>
              <div style="margin-top: 5px; color: #1f2937;">1</div>
            </div>
          </div>
        </div>

        <div style="margin-top: 20px; text-align: center; color: #6b7280; font-style: italic;">
          <p>Smart Retail, Bright Future - Receeto.com</p>
        </div>
      `;

      // Add the receipt card to the document temporarily
      document.body.appendChild(receiptCard);

      // Convert the receipt card to canvas
      const canvas = await html2canvas(receiptCard, {
        scale: 2,
        logging: false,
        useCORS: true,
        allowTaint: true
      });

      // Remove the temporary receipt card
      document.body.removeChild(receiptCard);

      // Calculate dimensions to fit the page width while maintaining aspect ratio
      let imgWidth = pageWidth - (2 * margin);
      let imgHeight = (canvas.height * imgWidth) / canvas.width;

      // If the receipt would go beyond the page, add a new page
      if (currentY + imgHeight > pageHeight - margin) {
        pdf.addPage();
        currentY = margin;
      }

      // Add the receipt image to the PDF
      pdf.addImage(
        canvas.toDataURL('image/png'),
        'PNG',
        margin,
        currentY,
        imgWidth,
        imgHeight
      );

      // Update the Y position for the next receipt
      currentY += imgHeight + 15;

      // Add a separator line if not the last receipt
      if (i < selectedTransactions.length - 1) {
        if (currentY + 5 > pageHeight - margin) {
          pdf.addPage();
          currentY = margin;
        } else {
          currentY += 5;
        }
      }
    }

    // Save the PDF
    pdf.save(`receeto-tickets-${new Date().toISOString().split('T')[0]}.pdf`);
  }

  openAddTicketModal() {
    this.showAddTicketModal = true;
    this.cdr.markForCheck();
  }

  handleTicketSave(ticketData: any) {
    // Generate a unique ticket number for the new ticket
    // Use the current timestamp to ensure uniqueness
    const uniqueTicketId = ticketData.ticketId || Math.floor(Date.now() / 1000);

    // Generate a unique product ID that doesn't conflict with existing ones
    const uniqueProductId = this.generateUniqueProductId(uniqueTicketId);

    // Add the new ticket to the transactions array
    const newTransaction = {
      ticketNumber: uniqueTicketId, // Ensure unique ticket number
      productId: uniqueProductId, // Use the generated unique product ID
      productName: ticketData.products.length > 0 ?
        (ticketData.products.length === 1 ?
          ticketData.products[0].name :
          ticketData.products.map((p: any) => p.name).join(', ')
        ) : 'General Purchase',
      productImage: 'assets/images/default-product.jpg',
      date: ticketData.date,
      amount: ticketData.totalAmount + ' TND',
      paymentMode: 'Cash',
      status: 'Completed',
      quantity: ticketData.products.length,
      rating: 0,
      brandName: ticketData.brandName,
      brandLogo: ticketData.isCollaboratingBrand ?
        `assets/images/brands/${ticketData.brandName === 'STRASS SOUSSE MALL' ? 'STRASS SOUSSE MALL.png' : ticketData.brandName.toLowerCase() + '.png'}` :
        'assets/images/default-brand-logo.png',
      // Include the products array for registered brands
      products: ticketData.isCollaboratingBrand ? ticketData.products : [],
      // Include QR code if available
      qrCodeImage: ticketData.qrCodeImage || null
    };

    // Add to transactions array
    this.transactions.unshift(newTransaction);

    // Save to localStorage for persistence - we DO save new tickets
    this.saveTransactionsToLocalStorage();

    // Update displayed transactions
    this.filterTransactions();
    this.cdr.markForCheck();
  }

  // Save transactions to localStorage for persistence - USER-SPECIFIC
  private saveTransactionsToLocalStorage() {
    try {
      // Get current authenticated user
      const currentUser = this.authService.currentUser();
      if (!currentUser?.email) {
        console.error('Cannot save transactions: No authenticated user');
        return;
      }

      // Use USER-SPECIFIC key for transactions
      const userTransactionKey = `receeto-transactions_${currentUser.email}`;
      localStorage.setItem(userTransactionKey, JSON.stringify(this.transactions.slice(0, 100)));

      console.log(`Transactions saved for user: ${currentUser.email} to key: ${userTransactionKey}`);
    } catch (e) {
      console.error('Error saving transactions to localStorage:', e);
    }
  }

  // Load transactions from localStorage - USER-SPECIFIC
  private loadTransactionsFromLocalStorage() {
    try {
      // Get current authenticated user
      const currentUser = this.authService.currentUser();
      if (!currentUser?.email) {
        console.log('No authenticated user, skipping transaction load');
        return;
      }

      // Use USER-SPECIFIC key for transactions
      const userTransactionKey = `receeto-transactions_${currentUser.email}`;
      const savedTransactions = localStorage.getItem(userTransactionKey);

      console.log(`Loading transactions for user: ${currentUser.email} from key: ${userTransactionKey}`);

      if (savedTransactions) {
        // Parse saved transactions
        const parsedTransactions = JSON.parse(savedTransactions);

        // Get all ticket numbers from original transactions
        const originalTicketNumbers = new Set(this.originalTransactions.map(t => t.ticketNumber));

        // Only add transactions that are not in the original data (newly added tickets)
        const newTransactions = parsedTransactions.filter((t: any) => !originalTicketNumbers.has(t.ticketNumber));

        // Check for duplicates in current transactions
        const existingTicketNumbers = new Set(this.transactions.map(t => t.ticketNumber));
        const uniqueNewTransactions = newTransactions.filter((t: any) => !existingTicketNumbers.has(t.ticketNumber));

        // Add new transactions to the beginning of the array
        if (uniqueNewTransactions.length > 0) {
          this.transactions = [...uniqueNewTransactions, ...this.transactions];
          console.log(`Loaded ${uniqueNewTransactions.length} new transactions for user: ${currentUser.email}`);
        }
      } else {
        console.log(`No saved transactions found for user: ${currentUser.email}`);
      }
    } catch (e) {
      console.error('Error loading transactions from localStorage:', e);
    }
  }

  isCollaboratingBrand(brandName: string): boolean {
    if (!brandName) return false;
    const brandLower = brandName.toLowerCase();
    const collaboratingBrands = ['zen', 'aziza', 'monoprix', 'carrefour', 'strass sousse mall']; // Added STRASS SOUSSE MALL
    return collaboratingBrands.includes(brandLower);
  }

  /**
   * Generate a unique product ID that doesn't conflict with existing ones
   * @param ticketId The ticket ID to use as a base for the product ID
   * @returns A unique product ID string
   */
  private generateUniqueProductId(ticketId: number): string {
    // Start with a base product ID format
    let productId = 'P-#' + ticketId;

    // Check if this product ID already exists in the transactions
    const existingIds = new Set(this.transactions.map(t => t.productId));

    // If the ID already exists, append a random suffix until it's unique
    if (existingIds.has(productId)) {
      let suffix = 1;
      while (existingIds.has(`${productId}-${suffix}`)) {
        suffix++;
      }
      productId = `${productId}-${suffix}`;
    }

    return productId;
  }

  /**
   * Reset the transactions array to the original data
   * This ensures that deleted tickets are restored when the page is refreshed
   */
  resetTransactionsToOriginal(): void {
    // Create a deep copy of the original transactions to avoid reference issues
    this.transactions = JSON.parse(JSON.stringify(this.originalTransactions));
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();

    // Remove the beforeunload event listener
    window.removeEventListener('beforeunload', () => {
      this.resetTransactionsToOriginal();
    });
  }

  selectAllTickets() {
    if (this.filteredTransactions.length === 0) return;

    // Select all tickets in filteredTransactions
    this.filteredTransactions.forEach(t => {
      this.selectedTickets.add(t.ticketNumber);
    });
    this.cdr.markForCheck();
  }

  handleBulkAction(action: 'delete' | 'export' | 'budget') {
    // Select all tickets first
    this.selectAllTickets();

    // Then perform the selected action
    switch (action) {
      case 'delete':
        this.deleteSelectedTickets();
        break;
      case 'export':
        this.exportSelectedTickets();
        break;
      case 'budget':
        this.addToBudget();
        break;
    }
    this.showSelectAllDropdown = false;
  }

  toggleSelectAllDropdown(event: MouseEvent) {
    event.stopPropagation();
    this.showSelectAllDropdown = !this.showSelectAllDropdown;
    this.cdr.markForCheck();
  }

  @HostListener('document:click')
  hideDropdown() {
    if (this.showSelectAllDropdown) {
      this.showSelectAllDropdown = false;
      this.cdr.markForCheck();
    }
  }
}